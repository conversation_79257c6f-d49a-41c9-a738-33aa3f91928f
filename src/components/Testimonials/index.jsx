'use client';
import { useTranslation } from '@/hooks/useTranslation';
import styles from './style.module.scss';
import TestimonialCarousel from './components/TestimonialCarousel';
import AnimatedLink from '@/components/AnimatedLink';

export default function Testimonials({ locale = 'fr' }) {
  const { t } = useTranslation('pages');

  // Récupérer les données de témoignages depuis les traductions
  const testimonialsData = [
    {
      name: t('agency.testimonials.testimonials_data.0.name'),
      content: t('agency.testimonials.testimonials_data.0.content')
    },
    {
      name: t('agency.testimonials.testimonials_data.1.name'),
      content: t('agency.testimonials.testimonials_data.1.content')
    },
    {
      name: t('agency.testimonials.testimonials_data.2.name'),
      content: t('agency.testimonials.testimonials_data.2.content')
    }
  ];

  return (
    <section className={styles.testimonials}>
      {/* Séparateur */}
      <div className={styles.separator}></div>
      
      {/* Section principale avec paragraphe à gauche et titre à droite */}
      <div className="container">
        <div className={styles.header}>
          <div className={styles.leftSection}>
            <p className={styles.sectionLabel}>
              {t('agency.testimonials.section_label')}
            </p>
          </div>
          
          <div className={styles.rightSection}>
            <h2 className={styles.sectionTitle}>
              {t('agency.testimonials.section_title')}
            </h2>
            <div className={styles.viewAllButton}>
              <AnimatedLink 
                href="#" 
                standalone={true}
                className={styles.viewAllLink}
              >
                {t('agency.testimonials.view_all_button')}
              </AnimatedLink>
            </div>
          </div>
        </div>
        
        {/* Carrousel des témoignages */}
        <div className={styles.carouselSection}>
          <TestimonialCarousel 
            testimonials={testimonialsData} 
            locale={locale}
          />
        </div>
      </div>
    </section>
  );
}
