'use client';
import { useTranslation } from '@/hooks/useTranslation';
import styles from './style.module.scss';
import TestimonialCarousel from './components/TestimonialCarousel';
import Rounded from '@/common/RoundedButton';

export default function Testimonials({ locale = 'fr' }) {
  const { t } = useTranslation('pages');

  // Récupérer les données de témoignages depuis les traductions
  const testimonialsData = [
    {
      name: t('agency.testimonials.testimonials_data.0.name'),
      content: t('agency.testimonials.testimonials_data.0.content')
    },
    {
      name: t('agency.testimonials.testimonials_data.1.name'),
      content: t('agency.testimonials.testimonials_data.1.content')
    },
    {
      name: t('agency.testimonials.testimonials_data.2.name'),
      content: t('agency.testimonials.testimonials_data.2.content')
    }
  ];

  return (
    <section className={styles.testimonials}>

      <div className="container">
        <hr/>

        <div className={styles.header}>
          <div className={styles.leftSection}>
            <p className="text-big">
              {t('agency.testimonials.section_label')}
            </p>
          </div>

          <div className={styles.rightSection}>
            <h2>
              {t('agency.testimonials.section_title')}
            </h2>
              <Rounded href={`https://g.page/r/CdYePvCaFA87EAE/review`} className={styles.viewAllButton}>
                <p>{t('agency.testimonials.view_all_button')}</p>
              </Rounded>
          </div>
        </div>

        {/* Carrousel des témoignages */}
        <div className={styles.carouselSection}>
          <TestimonialCarousel
            testimonials={testimonialsData}
            locale={locale}
          />
        </div>
      </div>
    </section>
  );
}
