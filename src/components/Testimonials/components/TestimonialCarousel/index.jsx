'use client';
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styles from './style.module.scss';
import TestimonialCard from '../TestimonialCard';

export default function TestimonialCarousel({ testimonials, locale = 'fr' }) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || !testimonials || testimonials.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000); // Change every 5 seconds

    return () => clearInterval(interval);
  }, [isAutoPlaying, testimonials]);

  const goToSlide = (index) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
    // Resume auto-play after 10 seconds of inactivity
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const goToPrevious = () => {
    const newIndex = currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1;
    goToSlide(newIndex);
  };

  const goToNext = () => {
    const newIndex = currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1;
    goToSlide(newIndex);
  };

  if (!testimonials || testimonials.length === 0) {
    return <div className={styles.noTestimonials}>Aucun témoignage disponible</div>;
  }

  return (
    <div className={styles.carousel}>
      {/* Navigation arrows */}
      <button 
        className={`${styles.navButton} ${styles.prevButton}`}
        onClick={goToPrevious}
        aria-label="Témoignage précédent"
      >
        <span className="material-symbols-outlined">chevron_left</span>
      </button>

      <button 
        className={`${styles.navButton} ${styles.nextButton}`}
        onClick={goToNext}
        aria-label="Témoignage suivant"
      >
        <span className="material-symbols-outlined">chevron_right</span>
      </button>

      {/* Testimonials container */}
      <div className={styles.testimonialsContainer}>
        <AnimatePresence mode="wait">
          <TestimonialCard
            key={currentIndex}
            testimonial={testimonials[currentIndex]}
            isActive={true}
          />
        </AnimatePresence>
      </div>

      {/* Dots indicator */}
      <div className={styles.dotsContainer}>
        {testimonials.map((_, index) => (
          <button
            key={index}
            className={`${styles.dot} ${index === currentIndex ? styles.activeDot : ''}`}
            onClick={() => goToSlide(index)}
            aria-label={`Aller au témoignage ${index + 1}`}
          />
        ))}
      </div>

      {/* Progress bar */}
      {isAutoPlaying && (
        <motion.div 
          className={styles.progressBar}
          initial={{ width: '0%' }}
          animate={{ width: '100%' }}
          transition={{ duration: 5, ease: 'linear' }}
          key={currentIndex}
        />
      )}
    </div>
  );
}
