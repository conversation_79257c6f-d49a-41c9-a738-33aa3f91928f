'use client';
import { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import styles from './style.module.scss';

export default function TestimonialCarousel({ testimonials, locale = 'fr' }) {
  const scrollContainerRef = useRef(null);
  const [scrollProgress, setScrollProgress] = useState(0);

  // Gérer le scroll et mettre à jour l'indicateur de progression
  const handleScroll = () => {
    if (!scrollContainerRef.current) return;

    const container = scrollContainerRef.current;
    const scrollLeft = container.scrollLeft;
    const maxScroll = container.scrollWidth - container.clientWidth;
    const progress = maxScroll > 0 ? scrollLeft / maxScroll : 0;

    setScrollProgress(progress);
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, []);

  if (!testimonials || testimonials.length === 0) {
    return <div className={styles.noTestimonials}>Aucun témoignage disponible</div>;
  }

  return (
    <div className={styles.carousel}>
      {/* Container de scroll horizontal */}
      <div
        ref={scrollContainerRef}
        className={styles.scrollContainer}
      >
        {testimonials.map((testimonial, index) => (
          <div key={index} className={styles.testimonialItem}>
            <div className={styles.testimonialCard}>
              <blockquote className={styles.quote}>
                "{testimonial.content}"
              </blockquote>
              <cite className={styles.author}>
                {testimonial.name}
              </cite>
            </div>
          </div>
        ))}
      </div>

      {/* Indicateur de progression en ligne */}
      <div className={styles.progressIndicator}>
        <div
          className={styles.progressBar}
          style={{ width: `${scrollProgress * 100}%` }}
        />
      </div>
    </div>
  );
}
