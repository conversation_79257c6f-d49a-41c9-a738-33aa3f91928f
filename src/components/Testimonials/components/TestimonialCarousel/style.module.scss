.carousel {
  position: relative;
  width: 100%;
}

.embla {
  overflow: hidden;
}

.emblaContainer {
  display: flex;
  gap: 24px;
  padding: 20px 0;
}

.emblaSlide {
  flex: 0 0 auto;
  width: 400px;
  min-width: 0;
}

.testimonialCard {
  background: #f5f5f5;
  border-radius: 12px;
  padding: 32px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

.quote {
  font-size: 16px;
  line-height: 1.5;
  color: #1c1d20;
  margin: 0 0 20px 0;
  font-style: normal;
  flex: 1;
}

.author {
  font-size: 14px;
  font-weight: 600;
  color: rgba(28, 29, 32, 0.8);
  font-style: normal;
  
  &::before {
    content: '— ';
    color: rgba(28, 29, 32, 0.4);
    font-weight: 400;
  }
}

.progressIndicator {
  width: 100%;
  height: 2px;
  background: rgba(28, 29, 32, 0.1);
  border-radius: 1px;
  margin-top: 24px;
  overflow: hidden;

  .progressBar {
    height: 100%;
    background: #1c1d20;
    border-radius: 1px;
    transition: width 0.1s ease;
  }
}

.noTestimonials {
  text-align: center;
  color: rgba(28, 29, 32, 0.6);
  font-style: italic;
  padding: 40px;
}

// Responsive design
@media (max-width: 768px) {
  .emblaSlide {
    width: 320px;
  }

  .testimonialCard {
    padding: 24px;
  }

  .quote {
    font-size: 15px;
  }

  .author {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .emblaContainer {
    gap: 16px;
  }

  .emblaSlide {
    width: 280px;
  }

  .testimonialCard {
    padding: 20px;
  }

  .quote {
    font-size: 14px;
    margin-bottom: 16px;
  }

  .progressIndicator {
    margin-top: 16px;
  }
}
