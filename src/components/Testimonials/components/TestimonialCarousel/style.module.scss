.carousel {
  position: relative;
  max-width: 800px;
  margin: 0 auto;

  .navButton {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border: 1px solid rgba(28, 29, 32, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    &:hover {
      background: #1c1d20;
      color: white;
      border-color: #1c1d20;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(-50%) scale(0.95);
    }

    span {
      font-size: 24px;
      line-height: 1;
    }

    &.prevButton {
      left: -24px;
    }

    &.nextButton {
      right: -24px;
    }
  }

  .testimonialsContainer {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dotsContainer {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 32px;

    .dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: none;
      background: rgba(28, 29, 32, 0.2);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(28, 29, 32, 0.4);
        transform: scale(1.1);
      }

      &.activeDot {
        background: #1c1d20;
        transform: scale(1.2);
      }
    }
  }

  .progressBar {
    position: absolute;
    bottom: -8px;
    left: 0;
    height: 2px;
    background: #1c1d20;
    border-radius: 1px;
  }

  .noTestimonials {
    text-align: center;
    color: rgba(28, 29, 32, 0.6);
    font-style: italic;
    padding: 40px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .carousel {
    .navButton {
      width: 40px;
      height: 40px;

      span {
        font-size: 20px;
      }

      &.prevButton {
        left: -20px;
      }

      &.nextButton {
        right: -20px;
      }
    }

    .dotsContainer {
      margin-top: 24px;
      gap: 8px;

      .dot {
        width: 10px;
        height: 10px;
      }
    }
  }
}

@media (max-width: 480px) {
  .carousel {
    .navButton {
      width: 36px;
      height: 36px;
      
      span {
        font-size: 18px;
      }

      &.prevButton {
        left: -18px;
      }

      &.nextButton {
        right: -18px;
      }
    }

    .testimonialsContainer {
      min-height: 180px;
    }

    .dotsContainer {
      margin-top: 20px;
      gap: 6px;

      .dot {
        width: 8px;
        height: 8px;
      }
    }
  }
}
