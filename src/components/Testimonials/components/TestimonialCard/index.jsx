'use client';
import { motion } from 'framer-motion';
import styles from './style.module.scss';

export default function TestimonialCard({ testimonial, isActive = false }) {
  const cardVariants = {
    hidden: { 
      opacity: 0, 
      y: 20,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      scale: 0.95,
      transition: {
        duration: 0.4,
        ease: "easeIn"
      }
    }
  };

  return (
    <motion.div 
      className={`${styles.testimonialCard} ${isActive ? styles.active : ''}`}
      variants={cardVariants}
      initial="hidden"
      animate={isActive ? "visible" : "hidden"}
      exit="exit"
      layout
    >
      <div className={styles.content}>
        <blockquote className={styles.quote}>
          "{testimonial.content}"
        </blockquote>
        
        <div className={styles.author}>
          <cite className={styles.authorName}>
            {testimonial.name}
          </cite>
        </div>
      </div>
    </motion.div>
  );
}
