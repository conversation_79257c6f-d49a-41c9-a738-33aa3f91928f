.testimonialCard {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(28, 29, 32, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #1c1d20 0%, rgba(28, 29, 32, 0.5) 100%);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.6s ease;
  }

  &.active {
    &::before {
      transform: scaleX(1);
    }
  }

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  .content {
    .quote {
      font-size: 18px;
      line-height: 1.6;
      color: #1c1d20;
      margin: 0 0 24px 0;
      font-style: normal;
      position: relative;

      &::before {
        content: '"';
        font-size: 48px;
        color: rgba(28, 29, 32, 0.2);
        position: absolute;
        top: -10px;
        left: -20px;
        font-family: Georgia, serif;
        line-height: 1;
      }
    }

    .author {
      .authorName {
        font-size: 16px;
        font-weight: 600;
        color: rgba(28, 29, 32, 0.8);
        font-style: normal;
        position: relative;

        &::before {
          content: '— ';
          color: rgba(28, 29, 32, 0.4);
          font-weight: 400;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .testimonialCard {
    padding: 32px 24px;

    .content {
      .quote {
        font-size: 16px;
        margin-bottom: 20px;

        &::before {
          font-size: 36px;
          top: -8px;
          left: -16px;
        }
      }

      .author {
        .authorName {
          font-size: 14px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .testimonialCard {
    padding: 24px 20px;

    .content {
      .quote {
        font-size: 15px;
        line-height: 1.5;

        &::before {
          font-size: 32px;
          top: -6px;
          left: -14px;
        }
      }
    }
  }
}
